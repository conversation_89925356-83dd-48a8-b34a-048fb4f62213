import { asyncHand<PERSON> } from "../utils/asyncHandler.js";
import { ApiError } from "../utils/ApiError.js";
import { ApiResponse } from "../utils/ApiResponse.js";
import { Seat, TimeSlot, SeatBooking } from "../models/seat.model.js";
import { Center } from "../models/center.model.js";
import { User } from "../models/user.model.js";

// Create seats for a center (admin only)
const createSeats = asyncHandler(async (req, res) => {
    try {
        if (req.user.role !== "admin") {
            throw new ApiError(403, "Unauthorized access: Only admins can create seats");
        }

        const { centerId } = req.params;
        const { seats } = req.body; // Array of seat objects

        if (!seats || !Array.isArray(seats) || seats.length === 0) {
            throw new ApiError(400, "Seats array is required");
        }

        const center = await Center.findById(centerId);
        if (!center) {
            throw new ApiError(404, "Center not found");
        }

        // Validate and create seats
        const createdSeats = [];
        for (const seatData of seats) {
            const { row, column, seatType, facilities, notes } = seatData;
            
            if (!row || !column) {
                throw new ApiError(400, "Row and column are required for each seat");
            }

            // Check if seat already exists
            const existingSeat = await Seat.findOne({ center: centerId, row, column });
            if (existingSeat) {
                throw new ApiError(400, `Seat ${row}${column} already exists in this center`);
            }

            const seat = await Seat.create({
                center: centerId,
                row,
                column,
                seatType: seatType || "regular",
                facilities: facilities || [],
                notes: notes || ""
            });

            createdSeats.push(seat);
        }

        return res.status(201).json(
            new ApiResponse(201, createdSeats, "Seats created successfully")
        );
    } catch (error) {
        throw new ApiError(error.statusCode || 500, error.message || "Failed to create seats");
    }
});

// Get all seats for a center
const getCenterSeats = asyncHandler(async (req, res) => {
    try {
        const { centerId } = req.params;
        const { includeInactive = false } = req.query;

        const center = await Center.findById(centerId);
        if (!center) {
            throw new ApiError(404, "Center not found");
        }

        const filter = { center: centerId };
        if (!includeInactive) {
            filter.isActive = true;
        }

        const seats = await Seat.find(filter)
            .sort({ row: 1, column: 1 })
            .populate("center", "name location");

        return res.status(200).json(
            new ApiResponse(200, seats, "Seats fetched successfully")
        );
    } catch (error) {
        throw new ApiError(error.statusCode || 500, error.message || "Failed to fetch seats");
    }
});

// Create time slots for a center (admin only)
const createTimeSlots = asyncHandler(async (req, res) => {
    try {
        if (req.user.role !== "admin") {
            throw new ApiError(403, "Unauthorized access: Only admins can create time slots");
        }

        const { centerId } = req.params;
        const { name, startTime, endTime, daysOfWeek, maxBookingDuration, price } = req.body;

        if (!name || !startTime || !endTime || !daysOfWeek || !Array.isArray(daysOfWeek)) {
            throw new ApiError(400, "Name, start time, end time, and days of week are required");
        }

        const center = await Center.findById(centerId);
        if (!center) {
            throw new ApiError(404, "Center not found");
        }

        const timeSlot = await TimeSlot.create({
            center: centerId,
            name,
            startTime,
            endTime,
            daysOfWeek,
            maxBookingDuration: maxBookingDuration || 180,
            price: price || 0
        });

        return res.status(201).json(
            new ApiResponse(201, timeSlot, "Time slot created successfully")
        );
    } catch (error) {
        throw new ApiError(error.statusCode || 500, error.message || "Failed to create time slot");
    }
});

// Get time slots for a center
const getCenterTimeSlots = asyncHandler(async (req, res) => {
    try {
        const { centerId } = req.params;
        const { includeInactive = false } = req.query;

        const center = await Center.findById(centerId);
        if (!center) {
            throw new ApiError(404, "Center not found");
        }

        const filter = { center: centerId };
        if (!includeInactive) {
            filter.isActive = true;
        }

        const timeSlots = await TimeSlot.find(filter)
            .sort({ startTime: 1 })
            .populate("center", "name location");

        return res.status(200).json(
            new ApiResponse(200, timeSlots, "Time slots fetched successfully")
        );
    } catch (error) {
        throw new ApiError(error.statusCode || 500, error.message || "Failed to fetch time slots");
    }
});

// Get available seats for a specific date and time slot
const getAvailableSeats = asyncHandler(async (req, res) => {
    try {
        const { centerId, timeSlotId } = req.params;
        const { date, startTime, endTime } = req.query;

        if (!date || !startTime || !endTime) {
            throw new ApiError(400, "Date, start time, and end time are required");
        }

        const center = await Center.findById(centerId);
        if (!center) {
            throw new ApiError(404, "Center not found");
        }

        const timeSlot = await TimeSlot.findById(timeSlotId);
        if (!timeSlot) {
            throw new ApiError(404, "Time slot not found");
        }

        // Get all active seats for the center
        const allSeats = await Seat.find({ center: centerId, isActive: true })
            .sort({ row: 1, column: 1 });

        // Get existing bookings for the date and time range
        const bookingDate = new Date(date);
        const existingBookings = await SeatBooking.find({
            bookingDate,
            status: { $in: ["confirmed", "completed"] },
            $or: [
                {
                    startTime: { $lt: endTime },
                    endTime: { $gt: startTime }
                }
            ]
        }).populate("seat");

        // Filter out booked seats
        const bookedSeatIds = existingBookings.map(booking => booking.seat._id.toString());
        const availableSeats = allSeats.filter(seat => 
            !bookedSeatIds.includes(seat._id.toString())
        );

        return res.status(200).json(
            new ApiResponse(200, {
                availableSeats,
                totalSeats: allSeats.length,
                bookedSeats: existingBookings.length
            }, "Available seats fetched successfully")
        );
    } catch (error) {
        throw new ApiError(error.statusCode || 500, error.message || "Failed to fetch available seats");
    }
});

export {
    createSeats,
    getCenterSeats,
    createTimeSlots,
    getCenterTimeSlots,
    getAvailableSeats
};
