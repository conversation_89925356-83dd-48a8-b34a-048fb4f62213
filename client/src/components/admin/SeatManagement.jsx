import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { customFetch } from '../../utils/customFetch';
import { seatAPI } from '../../services/seatAPI';

const SeatManagement = () => {
    const [centers, setCenters] = useState([]);
    const [selectedCenter, setSelectedCenter] = useState(null);
    const [seats, setSeats] = useState([]);
    const [timeSlots, setTimeSlots] = useState([]);
    const [bookings, setBookings] = useState([]);
    const [loading, setLoading] = useState(true);
    const [activeTab, setActiveTab] = useState('seats');

    // Form states
    const [showSeatForm, setShowSeatForm] = useState(false);
    const [showTimeSlotForm, setShowTimeSlotForm] = useState(false);
    const [seatFormData, setSeatFormData] = useState({
        rows: 3,
        columns: 5,
        seatType: 'regular'
    });
    const [timeSlotFormData, setTimeSlotFormData] = useState({
        name: '',
        startTime: '',
        endTime: '',
        daysOfWeek: [],
        maxBookingDuration: 180,
        price: 0
    });

    useEffect(() => {
        fetchCenters();
    }, []);

    useEffect(() => {
        if (selectedCenter) {
            fetchCenterData();
        }
    }, [selectedCenter]);

    const fetchCenters = async () => {
        try {
            const response = await customFetch('/api/v1/centers');
            setCenters(response.data.centers || []);
        } catch (error) {
            console.error('Error fetching centers:', error);
            toast.error('Failed to fetch centers');
        } finally {
            setLoading(false);
        }
    };

    const fetchCenterData = async () => {
        if (!selectedCenter) return;

        try {
            const [seatsResponse, timeSlotsResponse, bookingsResponse] = await Promise.all([
                seatAPI.getCenterSeats(selectedCenter._id, true),
                seatAPI.getCenterTimeSlots(selectedCenter._id, true),
                seatAPI.getCenterBookings(selectedCenter._id, 1, 50)
            ]);

            setSeats(seatsResponse.data || []);
            setTimeSlots(timeSlotsResponse.data || []);
            setBookings(bookingsResponse.data.bookings || []);
        } catch (error) {
            console.error('Error fetching center data:', error);
            toast.error('Failed to fetch center data');
        }
    };

    const handleCreateSeats = async (e) => {
        e.preventDefault();
        
        const seats = [];
        const rows = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J'];
        
        for (let r = 0; r < seatFormData.rows; r++) {
            for (let c = 1; c <= seatFormData.columns; c++) {
                seats.push({
                    row: rows[r],
                    column: c,
                    seatType: seatFormData.seatType
                });
            }
        }

        try {
            await seatAPI.createSeats(selectedCenter._id, seats);
            toast.success('Seats created successfully');
            setShowSeatForm(false);
            fetchCenterData();
        } catch (error) {
            console.error('Error creating seats:', error);
            toast.error(error.response?.data?.message || 'Failed to create seats');
        }
    };

    const handleCreateTimeSlot = async (e) => {
        e.preventDefault();
        
        try {
            await seatAPI.createTimeSlot(selectedCenter._id, timeSlotFormData);
            toast.success('Time slot created successfully');
            setShowTimeSlotForm(false);
            setTimeSlotFormData({
                name: '',
                startTime: '',
                endTime: '',
                daysOfWeek: [],
                maxBookingDuration: 180,
                price: 0
            });
            fetchCenterData();
        } catch (error) {
            console.error('Error creating time slot:', error);
            toast.error(error.response?.data?.message || 'Failed to create time slot');
        }
    };

    const handleCheckIn = async (bookingId) => {
        try {
            await seatAPI.checkInUser(bookingId);
            toast.success('User checked in successfully');
            fetchCenterData();
        } catch (error) {
            console.error('Error checking in user:', error);
            toast.error(error.response?.data?.message || 'Failed to check in user');
        }
    };

    const handleCheckOut = async (bookingId) => {
        try {
            await seatAPI.checkOutUser(bookingId);
            toast.success('User checked out successfully');
            fetchCenterData();
        } catch (error) {
            console.error('Error checking out user:', error);
            toast.error(error.response?.data?.message || 'Failed to check out user');
        }
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    };

    const formatTime = (timeString) => {
        return new Date(`2000-01-01T${timeString}`).toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        });
    };

    if (loading) {
        return (
            <div className="flex justify-center items-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            </div>
        );
    }

    return (
        <div className="seat-management p-6">
            <div className="mb-6">
                <h1 className="text-2xl font-bold text-gray-800 mb-4">Seat Management</h1>
                
                {/* Center Selection */}
                <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        Select Center
                    </label>
                    <select
                        value={selectedCenter?._id || ''}
                        onChange={(e) => {
                            const center = centers.find(c => c._id === e.target.value);
                            setSelectedCenter(center);
                        }}
                        className="w-full max-w-md px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                        <option value="">Select a center</option>
                        {centers.map(center => (
                            <option key={center._id} value={center._id}>
                                {center.name} - {center.location}
                            </option>
                        ))}
                    </select>
                </div>
            </div>

            {selectedCenter && (
                <div>
                    {/* Center Info */}
                    <div className="bg-white rounded-lg shadow-md p-4 mb-6">
                        <h2 className="text-xl font-semibold text-gray-800 mb-2">
                            {selectedCenter.name}
                        </h2>
                        <p className="text-gray-600">{selectedCenter.location}</p>
                        <div className="mt-2 text-sm text-gray-600">
                            <span>Capacity: {selectedCenter.capacity}</span>
                            <span className="ml-4">Total Seats: {seats.length}</span>
                            <span className="ml-4">Time Slots: {timeSlots.length}</span>
                        </div>
                    </div>

                    {/* Tabs */}
                    <div className="mb-6">
                        <div className="border-b border-gray-200">
                            <nav className="-mb-px flex space-x-8">
                                {['seats', 'timeSlots', 'bookings'].map(tab => (
                                    <button
                                        key={tab}
                                        onClick={() => setActiveTab(tab)}
                                        className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                            activeTab === tab
                                                ? 'border-blue-500 text-blue-600'
                                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                        }`}
                                    >
                                        {tab === 'seats' && 'Seats'}
                                        {tab === 'timeSlots' && 'Time Slots'}
                                        {tab === 'bookings' && 'Bookings'}
                                    </button>
                                ))}
                            </nav>
                        </div>
                    </div>

                    {/* Tab Content */}
                    {activeTab === 'seats' && (
                        <div className="bg-white rounded-lg shadow-md p-6">
                            <div className="flex justify-between items-center mb-4">
                                <h3 className="text-lg font-semibold">Seats ({seats.length})</h3>
                                <button
                                    onClick={() => setShowSeatForm(true)}
                                    className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                                >
                                    Add Seats
                                </button>
                            </div>

                            {/* Seat Grid Display */}
                            {seats.length > 0 ? (
                                <div className="grid grid-cols-10 gap-2">
                                    {seats.map(seat => (
                                        <div
                                            key={seat._id}
                                            className={`w-8 h-8 rounded border-2 flex items-center justify-center text-xs font-semibold ${
                                                seat.isActive
                                                    ? 'bg-green-100 border-green-300 text-green-800'
                                                    : 'bg-gray-100 border-gray-300 text-gray-500'
                                            }`}
                                            title={`Seat ${seat.seatNumber} (${seat.seatType})`}
                                        >
                                            {seat.column}
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <p className="text-gray-500 text-center py-8">No seats created yet</p>
                            )}

                            {/* Seat Creation Form */}
                            {showSeatForm && (
                                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                                    <div className="bg-white rounded-lg p-6 w-full max-w-md">
                                        <h3 className="text-lg font-semibold mb-4">Create Seats</h3>
                                        <form onSubmit={handleCreateSeats}>
                                            <div className="space-y-4">
                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                                        Number of Rows
                                                    </label>
                                                    <input
                                                        type="number"
                                                        min="1"
                                                        max="10"
                                                        value={seatFormData.rows}
                                                        onChange={(e) => setSeatFormData({
                                                            ...seatFormData,
                                                            rows: parseInt(e.target.value)
                                                        })}
                                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                        required
                                                    />
                                                </div>
                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                                        Seats per Row
                                                    </label>
                                                    <input
                                                        type="number"
                                                        min="1"
                                                        max="20"
                                                        value={seatFormData.columns}
                                                        onChange={(e) => setSeatFormData({
                                                            ...seatFormData,
                                                            columns: parseInt(e.target.value)
                                                        })}
                                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                        required
                                                    />
                                                </div>
                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                                        Seat Type
                                                    </label>
                                                    <select
                                                        value={seatFormData.seatType}
                                                        onChange={(e) => setSeatFormData({
                                                            ...seatFormData,
                                                            seatType: e.target.value
                                                        })}
                                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                    >
                                                        <option value="regular">Regular</option>
                                                        <option value="premium">Premium</option>
                                                        <option value="vip">VIP</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div className="flex justify-end space-x-3 mt-6">
                                                <button
                                                    type="button"
                                                    onClick={() => setShowSeatForm(false)}
                                                    className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
                                                >
                                                    Cancel
                                                </button>
                                                <button
                                                    type="submit"
                                                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                                                >
                                                    Create Seats
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            )}
                        </div>
                    )}

                    {activeTab === 'timeSlots' && (
                        <div className="bg-white rounded-lg shadow-md p-6">
                            <div className="flex justify-between items-center mb-4">
                                <h3 className="text-lg font-semibold">Time Slots ({timeSlots.length})</h3>
                                <button
                                    onClick={() => setShowTimeSlotForm(true)}
                                    className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                                >
                                    Add Time Slot
                                </button>
                            </div>

                            {timeSlots.length > 0 ? (
                                <div className="space-y-3">
                                    {timeSlots.map(timeSlot => (
                                        <div key={timeSlot._id} className="border border-gray-200 rounded-lg p-4">
                                            <div className="flex justify-between items-start">
                                                <div>
                                                    <h4 className="font-semibold text-gray-800">{timeSlot.name}</h4>
                                                    <p className="text-gray-600">
                                                        {formatTime(timeSlot.startTime)} - {formatTime(timeSlot.endTime)}
                                                    </p>
                                                    <p className="text-sm text-gray-500">
                                                        Days: {timeSlot.daysOfWeek.join(', ')}
                                                    </p>
                                                    <p className="text-sm text-gray-500">
                                                        Max Duration: {timeSlot.maxBookingDuration} minutes
                                                    </p>
                                                    {timeSlot.price > 0 && (
                                                        <p className="text-sm text-green-600 font-medium">
                                                            ₹{timeSlot.price}/hour
                                                        </p>
                                                    )}
                                                </div>
                                                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                                    timeSlot.isActive 
                                                        ? 'bg-green-100 text-green-800' 
                                                        : 'bg-red-100 text-red-800'
                                                }`}>
                                                    {timeSlot.isActive ? 'Active' : 'Inactive'}
                                                </span>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <p className="text-gray-500 text-center py-8">No time slots created yet</p>
                            )}

                            {/* Time Slot Creation Form */}
                            {showTimeSlotForm && (
                                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                                    <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-screen overflow-y-auto">
                                        <h3 className="text-lg font-semibold mb-4">Create Time Slot</h3>
                                        <form onSubmit={handleCreateTimeSlot}>
                                            <div className="space-y-4">
                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                                        Name
                                                    </label>
                                                    <input
                                                        type="text"
                                                        value={timeSlotFormData.name}
                                                        onChange={(e) => setTimeSlotFormData({
                                                            ...timeSlotFormData,
                                                            name: e.target.value
                                                        })}
                                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                        placeholder="e.g., Morning Session"
                                                        required
                                                    />
                                                </div>
                                                <div className="grid grid-cols-2 gap-4">
                                                    <div>
                                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                                            Start Time
                                                        </label>
                                                        <input
                                                            type="time"
                                                            value={timeSlotFormData.startTime}
                                                            onChange={(e) => setTimeSlotFormData({
                                                                ...timeSlotFormData,
                                                                startTime: e.target.value
                                                            })}
                                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                            required
                                                        />
                                                    </div>
                                                    <div>
                                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                                            End Time
                                                        </label>
                                                        <input
                                                            type="time"
                                                            value={timeSlotFormData.endTime}
                                                            onChange={(e) => setTimeSlotFormData({
                                                                ...timeSlotFormData,
                                                                endTime: e.target.value
                                                            })}
                                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                            required
                                                        />
                                                    </div>
                                                </div>
                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                                        Days of Week
                                                    </label>
                                                    <div className="grid grid-cols-2 gap-2">
                                                        {['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'].map(day => (
                                                            <label key={day} className="flex items-center">
                                                                <input
                                                                    type="checkbox"
                                                                    checked={timeSlotFormData.daysOfWeek.includes(day)}
                                                                    onChange={(e) => {
                                                                        const days = e.target.checked
                                                                            ? [...timeSlotFormData.daysOfWeek, day]
                                                                            : timeSlotFormData.daysOfWeek.filter(d => d !== day);
                                                                        setTimeSlotFormData({
                                                                            ...timeSlotFormData,
                                                                            daysOfWeek: days
                                                                        });
                                                                    }}
                                                                    className="mr-2"
                                                                />
                                                                <span className="text-sm capitalize">{day}</span>
                                                            </label>
                                                        ))}
                                                    </div>
                                                </div>
                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                                        Max Booking Duration (minutes)
                                                    </label>
                                                    <input
                                                        type="number"
                                                        min="30"
                                                        max="480"
                                                        step="30"
                                                        value={timeSlotFormData.maxBookingDuration}
                                                        onChange={(e) => setTimeSlotFormData({
                                                            ...timeSlotFormData,
                                                            maxBookingDuration: parseInt(e.target.value)
                                                        })}
                                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                        required
                                                    />
                                                </div>
                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                                        Price per Hour (₹)
                                                    </label>
                                                    <input
                                                        type="number"
                                                        min="0"
                                                        step="0.01"
                                                        value={timeSlotFormData.price}
                                                        onChange={(e) => setTimeSlotFormData({
                                                            ...timeSlotFormData,
                                                            price: parseFloat(e.target.value) || 0
                                                        })}
                                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                    />
                                                </div>
                                            </div>
                                            <div className="flex justify-end space-x-3 mt-6">
                                                <button
                                                    type="button"
                                                    onClick={() => {
                                                        setShowTimeSlotForm(false);
                                                        setTimeSlotFormData({
                                                            name: '',
                                                            startTime: '',
                                                            endTime: '',
                                                            daysOfWeek: [],
                                                            maxBookingDuration: 180,
                                                            price: 0
                                                        });
                                                    }}
                                                    className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
                                                >
                                                    Cancel
                                                </button>
                                                <button
                                                    type="submit"
                                                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                                                >
                                                    Create Time Slot
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            )}
                        </div>
                    )}

                    {activeTab === 'bookings' && (
                        <div className="bg-white rounded-lg shadow-md p-6">
                            <h3 className="text-lg font-semibold mb-4">Recent Bookings ({bookings.length})</h3>
                            
                            {bookings.length > 0 ? (
                                <div className="space-y-4">
                                    {bookings.map(booking => (
                                        <div key={booking._id} className="border border-gray-200 rounded-lg p-4">
                                            <div className="flex justify-between items-start mb-3">
                                                <div>
                                                    <h4 className="font-semibold text-gray-800">
                                                        {booking.user?.fullName}
                                                    </h4>
                                                    <p className="text-sm text-gray-600">{booking.user?.email}</p>
                                                </div>
                                                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                                    booking.status === 'confirmed' ? 'bg-green-100 text-green-800' :
                                                    booking.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                                                    booking.status === 'completed' ? 'bg-blue-100 text-blue-800' :
                                                    'bg-gray-100 text-gray-800'
                                                }`}>
                                                    {booking.status.toUpperCase()}
                                                </span>
                                            </div>
                                            
                                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3">
                                                <div>
                                                    <p className="text-xs text-gray-500 uppercase tracking-wide">Seat</p>
                                                    <p className="font-medium">{booking.seat?.seatNumber}</p>
                                                </div>
                                                <div>
                                                    <p className="text-xs text-gray-500 uppercase tracking-wide">Date</p>
                                                    <p className="font-medium">{formatDate(booking.bookingDate)}</p>
                                                </div>
                                                <div>
                                                    <p className="text-xs text-gray-500 uppercase tracking-wide">Time</p>
                                                    <p className="font-medium">
                                                        {formatTime(booking.startTime)} - {formatTime(booking.endTime)}
                                                    </p>
                                                </div>
                                                <div>
                                                    <p className="text-xs text-gray-500 uppercase tracking-wide">Amount</p>
                                                    <p className="font-medium text-green-600">₹{booking.totalAmount}</p>
                                                </div>
                                            </div>

                                            {booking.status === 'confirmed' && (
                                                <div className="flex gap-4 text-sm mb-3">
                                                    <span className={`${booking.checkedIn ? 'text-green-600' : 'text-gray-500'}`}>
                                                        Check-in: {booking.checkedIn ? '✓' : '✗'}
                                                    </span>
                                                    <span className={`${booking.checkedOut ? 'text-green-600' : 'text-gray-500'}`}>
                                                        Check-out: {booking.checkedOut ? '✓' : '✗'}
                                                    </span>
                                                </div>
                                            )}

                                            {booking.status === 'confirmed' && (
                                                <div className="flex gap-2">
                                                    {!booking.checkedIn && (
                                                        <button
                                                            onClick={() => handleCheckIn(booking._id)}
                                                            className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700"
                                                        >
                                                            Check In
                                                        </button>
                                                    )}
                                                    {booking.checkedIn && !booking.checkedOut && (
                                                        <button
                                                            onClick={() => handleCheckOut(booking._id)}
                                                            className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
                                                        >
                                                            Check Out
                                                        </button>
                                                    )}
                                                </div>
                                            )}
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <p className="text-gray-500 text-center py-8">No bookings found</p>
                            )}
                        </div>
                    )}
                </div>
            )}
        </div>
    );
};

export default SeatManagement;
