import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { seatAPI } from '../../services/seatAPI';
import SeatLayout from './SeatLayout';

const SeatBookingForm = ({ center, onBookingSuccess }) => {
    const [timeSlots, setTimeSlots] = useState([]);
    const [seats, setSeats] = useState([]);
    const [availableSeats, setAvailableSeats] = useState([]);
    const [selectedTimeSlot, setSelectedTimeSlot] = useState(null);
    const [selectedSeat, setSelectedSeat] = useState(null);
    const [bookingDate, setBookingDate] = useState('');
    const [startTime, setStartTime] = useState('');
    const [endTime, setEndTime] = useState('');
    const [bookingNotes, setBookingNotes] = useState('');
    const [loading, setLoading] = useState(false);
    const [loadingAvailability, setLoadingAvailability] = useState(false);

    useEffect(() => {
        if (center) {
            fetchTimeSlots();
            fetchSeats();
        }
    }, [center]);

    useEffect(() => {
        if (selectedTimeSlot && bookingDate && startTime && endTime) {
            fetchAvailableSeats();
        }
    }, [selectedTimeSlot, bookingDate, startTime, endTime]);

    const fetchTimeSlots = async () => {
        try {
            const response = await seatAPI.getCenterTimeSlots(center._id);
            setTimeSlots(response.data || []);
        } catch (error) {
            console.error('Error fetching time slots:', error);
            toast.error('Failed to fetch time slots');
        }
    };

    const fetchSeats = async () => {
        try {
            const response = await seatAPI.getCenterSeats(center._id);
            setSeats(response.data || []);
        } catch (error) {
            console.error('Error fetching seats:', error);
            toast.error('Failed to fetch seats');
        }
    };

    const fetchAvailableSeats = async () => {
        if (!selectedTimeSlot || !bookingDate || !startTime || !endTime) return;

        setLoadingAvailability(true);
        try {
            const response = await seatAPI.getAvailableSeats(
                center._id,
                selectedTimeSlot._id,
                bookingDate,
                startTime,
                endTime
            );
            setAvailableSeats(response.data.availableSeats || []);
            
            // Reset selected seat if it's no longer available
            if (selectedSeat && !response.data.availableSeats.some(seat => seat._id === selectedSeat._id)) {
                setSelectedSeat(null);
            }
        } catch (error) {
            console.error('Error fetching available seats:', error);
            toast.error('Failed to check seat availability');
            setAvailableSeats([]);
        } finally {
            setLoadingAvailability(false);
        }
    };

    const handleTimeSlotChange = (timeSlotId) => {
        const timeSlot = timeSlots.find(ts => ts._id === timeSlotId);
        setSelectedTimeSlot(timeSlot);
        setSelectedSeat(null);
        
        if (timeSlot) {
            // Set default times based on time slot
            if (!startTime) setStartTime(timeSlot.startTime);
            if (!endTime) setEndTime(timeSlot.endTime);
        }
    };

    const calculateDuration = () => {
        if (!startTime || !endTime) return 0;
        
        const start = new Date(`2000-01-01T${startTime}:00`);
        const end = new Date(`2000-01-01T${endTime}:00`);
        
        return Math.max(0, (end - start) / (1000 * 60)); // Duration in minutes
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (!selectedSeat || !selectedTimeSlot || !bookingDate || !startTime || !endTime) {
            toast.error('Please fill in all required fields');
            return;
        }

        const duration = calculateDuration();
        if (duration <= 0) {
            toast.error('End time must be after start time');
            return;
        }

        if (duration > selectedTimeSlot.maxBookingDuration) {
            toast.error(`Booking duration cannot exceed ${selectedTimeSlot.maxBookingDuration} minutes`);
            return;
        }

        setLoading(true);
        try {
            const bookingData = {
                seatId: selectedSeat._id,
                timeSlotId: selectedTimeSlot._id,
                bookingDate,
                startTime,
                endTime,
                bookingNotes
            };

            const response = await seatAPI.createBooking(bookingData);
            toast.success('Seat booked successfully!');
            
            if (onBookingSuccess) {
                onBookingSuccess(response.data);
            }
            
            // Reset form
            setSelectedSeat(null);
            setBookingNotes('');
            
        } catch (error) {
            console.error('Error creating booking:', error);
            toast.error(error.response?.data?.message || 'Failed to book seat');
        } finally {
            setLoading(false);
        }
    };

    const getTodayDate = () => {
        return new Date().toISOString().split('T')[0];
    };

    return (
        <div className="seat-booking-form">
            <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-2xl font-bold text-gray-800 mb-6">Book a Seat</h2>
                
                <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Center Info */}
                    <div className="bg-gray-50 p-4 rounded-lg">
                        <h3 className="font-semibold text-gray-800">{center.name}</h3>
                        <p className="text-gray-600">{center.location}</p>
                    </div>

                    {/* Date Selection */}
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Booking Date *
                        </label>
                        <input
                            type="date"
                            value={bookingDate}
                            onChange={(e) => setBookingDate(e.target.value)}
                            min={getTodayDate()}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            required
                        />
                    </div>

                    {/* Time Slot Selection */}
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Time Slot *
                        </label>
                        <select
                            value={selectedTimeSlot?._id || ''}
                            onChange={(e) => handleTimeSlotChange(e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            required
                        >
                            <option value="">Select a time slot</option>
                            {timeSlots.map(timeSlot => (
                                <option key={timeSlot._id} value={timeSlot._id}>
                                    {timeSlot.name} ({timeSlot.startTime} - {timeSlot.endTime})
                                    {timeSlot.price > 0 && ` - ₹${timeSlot.price}/hour`}
                                </option>
                            ))}
                        </select>
                    </div>

                    {/* Time Selection */}
                    <div className="grid grid-cols-2 gap-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Start Time *
                            </label>
                            <input
                                type="time"
                                value={startTime}
                                onChange={(e) => setStartTime(e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                End Time *
                            </label>
                            <input
                                type="time"
                                value={endTime}
                                onChange={(e) => setEndTime(e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required
                            />
                        </div>
                    </div>

                    {/* Duration Display */}
                    {startTime && endTime && (
                        <div className="text-sm text-gray-600">
                            Duration: {Math.floor(calculateDuration() / 60)}h {calculateDuration() % 60}m
                            {selectedTimeSlot && selectedTimeSlot.price > 0 && (
                                <span className="ml-2 font-semibold">
                                    (₹{((calculateDuration() / 60) * selectedTimeSlot.price).toFixed(2)})
                                </span>
                            )}
                        </div>
                    )}

                    {/* Seat Layout */}
                    {selectedTimeSlot && bookingDate && startTime && endTime && (
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Select Seat *
                            </label>
                            {loadingAvailability ? (
                                <div className="text-center py-8">
                                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
                                    <p className="mt-2 text-gray-600">Checking availability...</p>
                                </div>
                            ) : (
                                <SeatLayout
                                    seats={seats}
                                    availableSeats={availableSeats}
                                    selectedSeat={selectedSeat}
                                    onSeatSelect={setSelectedSeat}
                                    showAvailabilityOnly={true}
                                />
                            )}
                        </div>
                    )}

                    {/* Booking Notes */}
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Notes (Optional)
                        </label>
                        <textarea
                            value={bookingNotes}
                            onChange={(e) => setBookingNotes(e.target.value)}
                            rows={3}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="Any special requirements or notes..."
                        />
                    </div>

                    {/* Submit Button */}
                    <button
                        type="submit"
                        disabled={loading || !selectedSeat}
                        className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                        {loading ? 'Booking...' : 'Book Seat'}
                    </button>
                </form>
            </div>
        </div>
    );
};

export default SeatBookingForm;
