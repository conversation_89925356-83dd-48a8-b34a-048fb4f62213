import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';

const SeatLayout = ({ 
    seats = [], 
    availableSeats = [], 
    selectedSeat, 
    onSeatSelect, 
    showAvailabilityOnly = false 
}) => {
    const [seatGrid, setSeatGrid] = useState({});

    useEffect(() => {
        // Organize seats into a grid structure
        const grid = {};
        seats.forEach(seat => {
            if (!grid[seat.row]) {
                grid[seat.row] = {};
            }
            grid[seat.row][seat.column] = seat;
        });
        setSeatGrid(grid);
    }, [seats]);

    const getSeatStatus = (seat) => {
        if (!seat.isActive) return 'inactive';
        
        if (showAvailabilityOnly) {
            const isAvailable = availableSeats.some(availableSeat => 
                availableSeat._id === seat._id
            );
            return isAvailable ? 'available' : 'booked';
        }
        
        return 'available';
    };

    const getSeatClassName = (seat, status) => {
        const baseClasses = 'w-12 h-12 m-1 rounded-lg border-2 cursor-pointer transition-all duration-200 flex items-center justify-center text-sm font-semibold';
        
        const isSelected = selectedSeat && selectedSeat._id === seat._id;
        
        if (isSelected) {
            return `${baseClasses} bg-blue-500 border-blue-600 text-white shadow-lg transform scale-105`;
        }
        
        switch (status) {
            case 'available':
                return `${baseClasses} bg-green-100 border-green-300 text-green-800 hover:bg-green-200 hover:border-green-400`;
            case 'booked':
                return `${baseClasses} bg-red-100 border-red-300 text-red-800 cursor-not-allowed`;
            case 'inactive':
                return `${baseClasses} bg-gray-100 border-gray-300 text-gray-500 cursor-not-allowed`;
            default:
                return `${baseClasses} bg-gray-100 border-gray-300 text-gray-600`;
        }
    };

    const handleSeatClick = (seat) => {
        const status = getSeatStatus(seat);
        
        if (status === 'booked' || status === 'inactive') {
            toast.error(status === 'booked' ? 'This seat is already booked' : 'This seat is not available');
            return;
        }
        
        if (onSeatSelect) {
            onSeatSelect(seat);
        }
    };

    const getSeatTypeIcon = (seatType) => {
        switch (seatType) {
            case 'premium':
                return '⭐';
            case 'vip':
                return '👑';
            default:
                return '';
        }
    };

    const rows = Object.keys(seatGrid).sort();
    
    if (rows.length === 0) {
        return (
            <div className="text-center py-8 text-gray-500">
                No seats available for this center
            </div>
        );
    }

    return (
        <div className="seat-layout">
            {/* Legend */}
            <div className="mb-6 flex flex-wrap gap-4 justify-center">
                <div className="flex items-center gap-2">
                    <div className="w-4 h-4 bg-green-100 border-2 border-green-300 rounded"></div>
                    <span className="text-sm text-gray-600">Available</span>
                </div>
                <div className="flex items-center gap-2">
                    <div className="w-4 h-4 bg-red-100 border-2 border-red-300 rounded"></div>
                    <span className="text-sm text-gray-600">Booked</span>
                </div>
                <div className="flex items-center gap-2">
                    <div className="w-4 h-4 bg-blue-500 border-2 border-blue-600 rounded"></div>
                    <span className="text-sm text-gray-600">Selected</span>
                </div>
                <div className="flex items-center gap-2">
                    <div className="w-4 h-4 bg-gray-100 border-2 border-gray-300 rounded"></div>
                    <span className="text-sm text-gray-600">Inactive</span>
                </div>
            </div>

            {/* Seat Grid */}
            <div className="seat-grid bg-white p-6 rounded-lg border border-gray-200 overflow-x-auto">
                <div className="min-w-max">
                    {rows.map(row => {
                        const rowSeats = seatGrid[row];
                        const columns = Object.keys(rowSeats).sort((a, b) => parseInt(a) - parseInt(b));
                        
                        return (
                            <div key={row} className="flex items-center mb-2">
                                {/* Row label */}
                                <div className="w-8 h-12 flex items-center justify-center font-bold text-gray-600 mr-2">
                                    {row}
                                </div>
                                
                                {/* Seats in row */}
                                <div className="flex">
                                    {columns.map(column => {
                                        const seat = rowSeats[column];
                                        const status = getSeatStatus(seat);
                                        
                                        return (
                                            <div
                                                key={`${row}-${column}`}
                                                className={getSeatClassName(seat, status)}
                                                onClick={() => handleSeatClick(seat)}
                                                title={`Seat ${seat.seatNumber} (${seat.seatType})`}
                                            >
                                                <div className="text-center">
                                                    <div className="text-xs">
                                                        {getSeatTypeIcon(seat.seatType)}
                                                    </div>
                                                    <div>{column}</div>
                                                </div>
                                            </div>
                                        );
                                    })}
                                </div>
                            </div>
                        );
                    })}
                </div>
            </div>

            {/* Selected seat info */}
            {selectedSeat && (
                <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h4 className="font-semibold text-blue-800 mb-2">Selected Seat</h4>
                    <div className="text-sm text-blue-700">
                        <p><strong>Seat:</strong> {selectedSeat.seatNumber}</p>
                        <p><strong>Type:</strong> {selectedSeat.seatType}</p>
                        {selectedSeat.facilities && selectedSeat.facilities.length > 0 && (
                            <p><strong>Facilities:</strong> {selectedSeat.facilities.join(', ')}</p>
                        )}
                        {selectedSeat.notes && (
                            <p><strong>Notes:</strong> {selectedSeat.notes}</p>
                        )}
                    </div>
                </div>
            )}
        </div>
    );
};

export default SeatLayout;
